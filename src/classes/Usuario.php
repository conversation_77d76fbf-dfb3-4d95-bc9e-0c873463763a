<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

// Import PDOException for better error handling

class Usuario
{
	// --- Atributos ---
	private ?int    $id       = null;
	private ?string $username = null;
	private ?string $clave    = null; // Almacenará el HASH de la clave
	private ?string $nombre   = null;
	private ?int    $estado   = null;
	private ?string $correo   = null; // Nuevo campo
	private ?string $cedula   = null; // Nuevo campo

	/**
	 * Constructor: Inicializa las propiedades del objeto Usuario.
	 */
	public function __construct()
	{
		$this->id       = 0; // O null si prefieres no usar 0 por defecto
		$this->username = null;
		$this->clave    = null;
		$this->nombre   = null;
		$this->estado   = 1; // Estado activo por defecto
		$this->correo   = null;
		$this->cedula   = null;
	}

	/**
	 * Método estático para construir un objeto Usuario desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos del usuario.
	 *
	 * @return self Instancia de Usuario.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto           = new self();
			$objeto->id       = isset($resultado['id']) ? (int)$resultado['id'] : 0;
			$objeto->username = $resultado['username'] ?? null;
			$objeto->clave    = $resultado['clave'] ?? null; // La clave viene hasheada de la DB
			$objeto->nombre   = $resultado['nombre'] ?? null;
			$objeto->estado   = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;
			$objeto->correo   = $resultado['correo'] ?? null; // Nuevo campo
			$objeto->cedula   = $resultado['cedula'] ?? null; // Nuevo campo
			return $objeto;
		} catch (Exception $e) {
			// Considera loggear el error aquí
			throw new Exception("Error al construir Usuario: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos (Estáticos) ---

	/**
	 * Valida las credenciales del usuario usando password_verify().
	 *
	 * @param string $username   Nombre de usuario.
	 * @param string $clavePlana Contraseña en texto plano a verificar.
	 * @param PDO    $conexion   Conexión PDO a la base de datos.
	 *
	 * @return self|null Devuelve el objeto Usuario si las credenciales son válidas, null en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function validarCredenciales(string $username, string $clavePlana, PDO $conexion): ?self
	{
		try {
			// Selecciona el usuario incluyendo el hash de la clave (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM usuarios
            WHERE
            	username = :username AND estado = 1
            LIMIT 1
            SQL;
			// Asume tabla 'usuarios'

			$statement = $conexion->prepare($query);
			$statement->bindValue(':username', $username, PDO::PARAM_STR);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			if ($resultado) {
				$claveHasheadaAlmacenada = $resultado['clave'];
				// Verifica la contraseña plana contra el hash almacenado de forma segura
				if (password_verify($clavePlana, $claveHasheadaAlmacenada)) {
					// La contraseña es correcta
					return self::construct($resultado);
				} else {
					// Contraseña incorrecta
					return null;
				}
			} else {
				// Usuario no encontrado o inactivo
				return null;
			}
		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al validar credenciales: " . $e->getMessage());
		}
	}

	/**
	 * Actualiza la contraseña de un usuario por su ID usando password_hash().
	 *
	 * @param int    $id              ID del usuario.
	 * @param string $nuevaClavePlana La nueva contraseña en texto plano.
	 * @param PDO    $conexion        Conexión PDO a la base de datos.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si la contraseña está vacía o si ocurre un error de base de datos o hash.
	 */
	public static function actualizarClave(int $id, string $nuevaClavePlana, PDO $conexion): bool
	{
		if (empty($nuevaClavePlana)) {
			throw new Exception("La nueva contraseña no puede estar vacía.");
		}
		try {
			// Crea el hash seguro de la nueva contraseña
			$nuevaClaveHasheada = password_hash($nuevaClavePlana, PASSWORD_DEFAULT);

			if (!$nuevaClaveHasheada) {
				throw new Exception("Error al generar el hash de la contraseña.");
			}

			// Actualiza la base de datos con el nuevo hash (Usando Heredoc)
			$query = <<<SQL
            UPDATE usuarios SET
            	clave = :clave
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':clave', $nuevaClaveHasheada, PDO::PARAM_STR);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			throw new Exception("Error de base de datos al actualizar clave: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene un usuario por su ID.
	 *
	 * @param int $id       ID del usuario.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Usuario o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener usuario por ID (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM usuarios
            WHERE
            	id = :id
            LIMIT 1
            SQL;
			// Asume tabla 'usuarios'

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Usuario (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de usuarios activos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Usuario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(PDO $conexion): array
	{
		try {
			// Consulta para obtener lista de usuarios activos (Usando Heredoc)
			$query = <<<SQL
            SELECT
            	*
            FROM usuarios
            WHERE
            	estado = 1
            ORDER BY
            	nombre
            SQL;

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Usuarios: " . $e->getMessage());
		}
	}

	/**
	 * Verifica si un correo ya existe en la base de datos.
	 *
	 * @param string $correo Correo a verificar.
	 * @param PDO $conexion Conexión PDO.
	 * @param int|null $excludeId ID del usuario a excluir de la búsqueda (para modificaciones).
	 *
	 * @return bool True si el correo ya existe, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	private static function correoExiste(string $correo, PDO $conexion, ?int $excludeId = null): bool
	{
		try {
			$query = <<<SQL
            SELECT COUNT(*) as count
            FROM usuarios
            WHERE correo = :correo
            SQL;

			if ($excludeId !== null) {
				$query .= " AND id != :excludeId";
			}

			$statement = $conexion->prepare($query);
			$statement->bindValue(':correo', $correo, PDO::PARAM_STR);

			if ($excludeId !== null) {
				$statement->bindValue(':excludeId', $excludeId, PDO::PARAM_INT);
			}

			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return (int)$resultado['count'] > 0;

		} catch (PDOException $e) {
			throw new Exception("Error al verificar correo duplicado: " . $e->getMessage());
		}
	}

	/**
	 * Verifica si una cédula ya existe en la base de datos.
	 *
	 * @param string $cedula Cédula a verificar.
	 * @param PDO $conexion Conexión PDO.
	 * @param int|null $excludeId ID del usuario a excluir de la búsqueda (para modificaciones).
	 *
	 * @return bool True si la cédula ya existe, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	private static function cedulaExiste(string $cedula, PDO $conexion, ?int $excludeId = null): bool
	{
		try {
			$query = <<<SQL
            SELECT COUNT(*) as count
            FROM usuarios
            WHERE cedula = :cedula
            SQL;

			if ($excludeId !== null) {
				$query .= " AND id != :excludeId";
			}

			$statement = $conexion->prepare($query);
			$statement->bindValue(':cedula', $cedula, PDO::PARAM_STR);

			if ($excludeId !== null) {
				$statement->bindValue(':excludeId', $excludeId, PDO::PARAM_INT);
			}

			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return (int)$resultado['count'] > 0;

		} catch (PDOException $e) {
			throw new Exception("Error al verificar cédula duplicada: " . $e->getMessage());
		}
	}

	/**
	 * Crea un nuevo usuario en la base de datos a partir de un objeto Usuario.
	 * El objeto Usuario debe estar completamente poblado, incluyendo la clave
	 * (se asume que setClave() fue llamado previamente con la clave plana, generando el hash).
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo usuario creado o false en caso de error.
	 * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
	 */
	function crear(PDO $conexion): int|false
	{
		// Validaciones básicas sobre el objeto
		// Se verifica que el hash de la clave exista (implica que setClave fue llamado)
		if (empty($this->getUsername()) || empty($this->getClave()) || empty($this->getNombre())) {
			throw new Exception("Username, clave (hasheada) y nombre son requeridos en el objeto Usuario para crearlo.");
		}

		// Validar que correo no esté duplicado si se proporciona
		if (!empty($this->getCorreo()) && self::correoExiste($this->getCorreo(), $conexion)) {
			throw new Exception("Error al crear usuario: El correo '{$this->getCorreo()}' ya existe.");
		}

		// Validar que cédula no esté duplicada si se proporciona
		if (!empty($this->getCedula()) && self::cedulaExiste($this->getCedula(), $conexion)) {
			throw new Exception("Error al crear usuario: La cédula '{$this->getCedula()}' ya existe.");
		}

		// Podríamos añadir más validaciones sobre el objeto si es necesario (ej. formato de username)

		try {
			// La contraseña ya debe estar hasheada en el objeto via setClave()
			$claveHasheada = $this->getClave();
			$username      = $this->getUsername(); // Para usar en mensaje de error

			// Preparar la consulta INSERT usando Heredoc
			$query = <<<SQL
            INSERT INTO usuarios (
            	 username
            	,clave
            	,nombre
            	,correo
            	,cedula
            ) VALUES (
            	 :username
            	,:clave
            	,:nombre
            	,:correo
            	,:cedula
            )
            SQL;
			// Asume tabla 'usuarios'

			$statement = $conexion->prepare($query);

			// Bind de parámetros desde el objeto
			$statement->bindValue(':username', mb_strtoupper($this->getUsername()), PDO::PARAM_STR);
			$statement->bindValue(':clave', $claveHasheada, PDO::PARAM_STR); // Usar el hash del objeto
			$statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);
			$statement->bindValue(':correo', $this->getCorreo(), PDO::PARAM_STR);
			$statement->bindValue(':cedula', $this->getCedula(), PDO::PARAM_STR);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID del usuario recién creado
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			// Manejar errores específicos de DB (ej. username duplicado)
			if ($e->getCode() == 23000 || $e->getCode() == 1062) { // Códigos comunes para violación de UNIQUE constraint
				throw new Exception("Error al crear usuario: El username '$username' ya existe.");
			} else {
				throw new Exception("Error de base de datos al crear usuario: " . $e->getMessage());
			}
		} catch (Exception $e) {
			// Capturar otros errores
			throw new Exception("Error al crear usuario: " . $e->getMessage());
		}
	}

	/**
	 * Modifica los datos de un usuario existente.
	 *
	 * @param int    $id       ID del usuario a modificar.
	 * @param string $nuevoNombre El nuevo nombre para el usuario.
	 * @param string|null $nuevoCorreo El nuevo correo para el usuario.
	 * @param string|null $nuevaCedula La nueva cédula para el usuario.
	 * @param PDO    $conexion Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si el nuevo nombre está vacío o si ocurre un error de base de datos.
	 */
	public static function modificar(int $id, string $nuevoNombre, ?string $nuevoCorreo = null, ?string $nuevaCedula = null, PDO $conexion): bool
	{
		if (empty(trim($nuevoNombre))) {
			throw new Exception("El nombre no puede estar vacío.");
		}

		// Validar que correo no esté duplicado si se proporciona (excluyendo el usuario actual)
		if (!empty($nuevoCorreo) && self::correoExiste($nuevoCorreo, $conexion, $id)) {
			throw new Exception("Error al modificar usuario: El correo '$nuevoCorreo' ya existe.");
		}

		// Validar que cédula no esté duplicada si se proporciona (excluyendo el usuario actual)
		if (!empty($nuevaCedula) && self::cedulaExiste($nuevaCedula, $conexion, $id)) {
			throw new Exception("Error al modificar usuario: La cédula '$nuevaCedula' ya existe.");
		}

		try {
			// Consulta para actualizar los datos
			$query = <<<SQL
            UPDATE usuarios SET
                nombre = :nombre,
                correo = :correo,
                cedula = :cedula
            WHERE
                id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':nombre', trim($nuevoNombre), PDO::PARAM_STR);
			$statement->bindValue(':correo', $nuevoCorreo, PDO::PARAM_STR);
			$statement->bindValue(':cedula', $nuevaCedula, PDO::PARAM_STR);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			// Podríamos chequear rowCount() > 0 si queremos asegurarnos que una fila fue afectada,
			// aunque si el nombre es el mismo, rowCount será 0 pero la operación es "exitosa".
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			// Considera loggear el error aquí
			throw new Exception("Error de base de datos al modificar usuario (ID: $id): " . $e->getMessage());
		}
	}


	/**
	 * Desactiva un usuario estableciendo su estado a 0.
	 *
	 * @param int $id       ID del usuario a desactivar.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la desactivación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function desactivar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado a 0 (inactivo)
			$query = <<<SQL
            UPDATE usuarios SET
            	estado = 0
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			// execute() devuelve true en éxito, false en error.
			// Podríamos chequear rowCount() > 0 si queremos asegurarnos que una fila fue afectada.
			return $statement->execute();

		} catch (PDOException $e) {
			// Error de base de datos
			// Considera loggear el error aquí
			throw new Exception("Error de base de datos al desactivar usuario (ID: $id): " . $e->getMessage());
		}
	}


	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getUsername(): ?string
	{
		return $this->username;
	}

	public function setUsername(?string $username): self
	{
		$this->username = $username;
		return $this;
	}

	/**
	 * Obtiene el HASH de la clave almacenada.
	 * @return string|null
	 */
	public function getClave(): ?string
	{
		return $this->clave;
	}

	/**
	 * Establece la contraseña. Genera un hash seguro antes de asignarla.
	 *
	 * @param string|null $clavePlana La contraseña en texto plano. Null para borrarla.
	 *
	 * @return self
	 * @throws Exception Si hay error al generar el hash.
	 */
	public function setClave(?string $clavePlana): self
	{
		if ($clavePlana === null) {
			$this->clave = null;
		} else {
			// Usa el algoritmo por defecto (actualmente BCRYPT), que es seguro y recomendado
			$hash = password_hash($clavePlana, PASSWORD_DEFAULT);
			if (!$hash) {
				throw new Exception('Error al generar el hash de la contraseña.');
			}
			$this->clave = $hash; // Almacena el hash
		}
		return $this;
	}

	public function getNombre(): ?string
	{
		return $this->nombre;
	}

	public function setNombre(?string $nombre): self
	{
		$this->nombre = $nombre;
		return $this;
	}

	public function getEstado(): ?int
	{
		return $this->estado;
	}

	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	// Nuevos getters y setters para correo y cedula

	public function getCorreo(): ?string
	{
		return $this->correo;
	}

	public function setCorreo(?string $correo): self
	{
		$this->correo = $correo;
		return $this;
	}

	public function getCedula(): ?string
	{
		return $this->cedula;
	}

	public function setCedula(?string $cedula): self
	{
		$this->cedula = $cedula;
		return $this;
	}

	// --- Métodos adicionales (Ejemplos) ---

	/**
	 * Verifica si el usuario está activo.
	 * @return bool
	 */
	public function isActivo(): bool
	{
		// Asegúrate de que el estado no sea null antes de comparar
		return $this->estado === 1;
	}

}
